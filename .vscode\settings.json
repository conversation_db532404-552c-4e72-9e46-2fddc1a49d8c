{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "tailwindCSS.includeLanguages": {"typescript": "typescriptreact"}, "tailwindCSS.experimental.classRegex": ["tw`([^`]*)`", "tw=\"([^\"]*)\"", "tw={`([^`}]+)`}", "className=\"([^\"]*)\"", "className={`([^`}]+)`}"]}