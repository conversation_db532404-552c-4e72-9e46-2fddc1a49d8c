import ContactCard from '@/components/custom/contact-card';
import { ContactForm } from '@/components/forms/contact-form';
import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
	title: 'Careers - Join Sudha Software Solutions',
	description:
		'Explore exciting job opportunities at Sudha Software Solutions. Join a team of innovative professionals in web development, mobile development, and digital marketing. Build your career with us!',
	keywords:
		'Careers, Job Openings, Sudha Software Solutions, IT Jobs, Web Development Jobs, Mobile Development Careers, Digital Marketing Jobs, Software Engineering, Tech Careers',
	openGraph: {
		title: 'Careers - Join Sudha Software Solutions',
		description:
			'Looking for your next career move? Sudha Software Solutions is hiring! Discover job openings in software development, mobile apps, and digital marketing. Apply now and grow with us!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		url: 'https://careers.sudhasoftwaresolutions.com',
		type: 'website',
	},
	twitter: {
		title: 'Careers - Join Sudha Software Solutions',
		description:
			'Join a fast-growing tech company! Sudha Software Solutions is looking for talented professionals in web development, mobile apps, and digital marketing. Apply today!',
		images: [
			'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
		],
		card: 'summary_large_image',
		site: '@sudha_software_solutions',
		creator: '@sudha_software_solutions',
	},
};

const ContactPage = () => {
	return (
		<div className="relative flex w-full flex-col overflow-hidden rounded-md antialiased md:items-center md:justify-center">
			<section className="pt-10 md:pt-36">
				<div className="relative z-10 mx-auto w-full max-w-7xl p-4 pt-20 md:pt-0">
					<div className="relative mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
						<h1 className="mx-auto mb-5 max-w-2xl text-center text-4xl font-bold leading-[50px] text-gray-900 dark:text-white/80 md:text-5xl">
							Get in <span className="text-red-600">Touch</span>
						</h1>
						<p className="mx-auto mb-9 max-w-lg text-center text-base font-normal leading-7 text-gray-700 dark:text-white/60">
							We’d love to hear from you! Whether you have
							questions about current opportunities, need more
							information about our company, or simply want to
							share your thoughts, our team is here to help.
						</p>
					</div>
				</div>
			</section>

			<ContactCard>
				<h2 className="font-manrope mb-2 text-xl font-semibold leading-10 text-red-600 lg:text-4xl">
					Get In Touch
				</h2>
				<p className="text-md mb-6 text-gray-700 ">
					Interested in joining our team? <br /> Fill out the form
					below, and our hiring team will reach out soon.
				</p>
				<ContactForm />
			</ContactCard>
		</div>
	);
};

export default ContactPage;
