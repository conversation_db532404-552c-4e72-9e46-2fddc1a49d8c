"use client";

import React, { useState } from 'react';
import { Metadata } from 'next';
import Image from 'next/image';

export const metadata: Metadata = {
    title: 'Contact Us - Sudha Software Solutions',
    description:
        'Get in touch with Sudha Software Solutions. Contact our team for career opportunities, project inquiries, or any questions about our IT services. We\'re here to help you succeed.',
    keywords:
        'Contact, Sudha Software Solutions, Get in Touch, Career Inquiries, IT Services Contact, Software Development Contact, Project Inquiries, Support',
    openGraph: {
        title: 'Contact Us - Sudha Software Solutions',
        description:
            'Ready to start your journey with us? Contact Sudha Software Solutions for career opportunities, project discussions, or any questions about our services.',
        images: [
            'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
        ],
        url: 'https://careers.sudhasoftwaresolutions.com/contact',
        type: 'website',
    },
    twitter: {
        title: 'Contact Us - Sudha Software Solutions',
        description:
            'Get in touch with our team! Whether you\'re interested in career opportunities or have project inquiries, we\'re here to help.',
        images: [
            'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
        ],
        card: 'summary_large_image',
        site: '@sudha_software_solutions',
        creator: '@sudha_software_solutions',
    },
};

const ContactPage = () => {
    return (
        <div className="relative flex w-full flex-col overflow-hidden rounded-md antialiased md:items-center md:justify-center">
            <section className="pt-10 md:pt-36">
                <div className="relative z-10 mx-auto w-full max-w-7xl p-4 pt-20 md:pt-0">
                    <div className="relative mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
                        <h1 className="mx-auto mb-5 max-w-2xl text-center text-4xl font-bold leading-[50px] text-gray-900 dark:text-white/80 md:text-5xl">
                            Get in <span className="text-red-600">Touch</span>
                        </h1>
                        <p className="mx-auto mb-9 max-w-lg text-center text-base font-normal leading-7 text-gray-700 dark:text-white/60">
                            We’d love to hear from you! Whether you have
                            questions about current opportunities, need more
                            information about our company, or simply want to
                            share your thoughts, our team is here to help.
                        </p>
                    </div>
                </div>
            </section>

            <ContactCard>
                <h2 className="font-manrope mb-2 text-xl font-semibold leading-10 text-red-600 lg:text-4xl">
                    Get In Touch
                </h2>
                <p className="text-md mb-6 text-gray-700 ">
                    Interested in joining our team? <br /> Fill out the form
                    below, and our hiring team will reach out soon.
                </p>
                <ContactForm />
            </ContactCard>
        </div>
    );
};

export default ContactPage;
