"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { Metadata } from 'next';

// export const metadata: Metadata = {
//     title: 'Contact Us - Sudha Software Solutions',
//     description:
//         'Get in touch with Sudha Software Solutions. Contact our team for career opportunities, project inquiries, or any questions about our IT services.',
//     keywords:
//         'Contact, Sudha Software Solutions, Get in Touch, Career Inquiries, IT Services Contact, Software Development Contact',
//     openGraph: {
//         title: 'Contact Us - Sudha Software Solutions',
//         description:
//             'Ready to start your journey with us? Contact Sudha Software Solutions for career opportunities, project discussions, or any questions.',
//         images: [
//             'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
//         ],
//         url: 'https://careers.sudhasoftwaresolutions.com/contact',
//         type: 'website',
//     },
//     twitter: {
//         title: 'Contact Us - Sudha Software Solutions',
//         description:
//             'Get in touch with our team! Whether you are interested in career opportunities or have project inquiries, we are here to help.',
//         images: [
//             'https://careers.sudhasoftwaresolutions.com/assets/banner/careers-banner.jpg',
//         ],
//         card: 'summary_large_image',
//         site: '@sudha_software_solutions',
//         creator: '@sudha_software_solutions',
//     },
// };

const contactInfo = [
    {
        icon: '📧',
        title: 'Email Us',
        description: 'Send us an email and we will get back to you within 24 hours.',
        contact: '<EMAIL>',
        action: 'mailto:<EMAIL>'
    },
    {
        icon: '📞',
        title: 'Call Us',
        description: 'Speak directly with our team during business hours.',
        contact: '+91 (*************',
        action: 'tel:+************'
    },
    {
        icon: '📍',
        title: 'Visit Us',
        description: 'Come visit our office for an in-person conversation.',
        contact: 'Hyderabad, Telangana, India',
        action: '#'
    },
    {
        icon: '💬',
        title: 'Live Chat',
        description: 'Chat with our team in real-time for quick questions.',
        contact: 'Available 9 AM - 6 PM IST',
        action: '#'
    }
];

const ContactPage = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        setTimeout(() => {
            setIsSubmitting(false);
            setSubmitStatus('success');
            setFormData({
                name: '',
                email: '',
                phone: '',
                subject: '',
                message: ''
            });

            setTimeout(() => setSubmitStatus('idle'), 3000);
        }, 2000);
    };

    return (
        <main className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
            {/* Hero Section */}
            <section className="relative py-20 lg:py-32">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
                    <div className="grid items-center gap-12 lg:grid-cols-2">
                        <div className="space-y-8">
                            <div className="space-y-6">
                                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                                    Get in{' '}
                                    <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                                        Touch
                                    </span>
                                </h1>
                                <p className="text-lg text-gray-600 leading-relaxed">
                                    We would love to hear from you! Whether you have questions about current opportunities,
                                    need more information about our company, or simply want to share your thoughts,
                                    our team is here to help.
                                </p>
                            </div>
                        </div>
                        <div className="relative">
                            <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-20 blur-lg"></div>
                            <Image
                                src="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?w=600&h=600&fit=crop"
                                width={500}
                                height={500}
                                className="relative rounded-2xl shadow-xl"
                                alt="Contact us"
                                priority
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* Contact Methods Section */}
            <section className="py-20 lg:py-32 bg-white">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
                    <div className="text-center mb-16">
                        <div className="flex items-center justify-center gap-4 mb-4">
                            <div className="rounded-full bg-blue-100 p-3">
                                <span className="text-2xl">📞</span>
                            </div>
                            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                                Multiple Ways to{' '}
                                <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                                    Connect
                                </span>
                            </h2>
                        </div>
                        <p className="text-lg text-gray-600">
                            Choose the method that works best for you
                        </p>
                    </div>
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
                        {contactInfo.map((info, index) => (
                            <div key={index} className="rounded-lg bg-gray-50 p-6 text-center transition-all duration-300 hover:bg-gray-100 hover:shadow-md">
                                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white shadow-sm">
                                    <span className="text-2xl">{info.icon}</span>
                                </div>
                                <h3 className="mb-2 text-lg font-semibold text-gray-900">{info.title}</h3>
                                <p className="mb-4 text-sm text-gray-600">{info.description}</p>
                                <a
                                    href={info.action}
                                    className="text-sm font-medium text-red-600 hover:text-red-700 transition-colors"
                                >
                                    {info.contact}
                                </a>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Contact Form Section */}
            <section className="py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-indigo-50">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
                    <div className="grid items-center gap-12 lg:grid-cols-2">
                        <div className="relative order-2 lg:order-1">
                            <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-green-400 to-blue-400 opacity-20 blur-lg"></div>
                            <Image
                                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=600&fit=crop"
                                width={500}
                                height={500}
                                className="relative rounded-2xl shadow-xl"
                                alt="Contact form"
                            />
                        </div>
                        <div className="order-1 space-y-8 lg:order-2">
                            <div className="flex items-center gap-4">
                                <div className="rounded-full bg-green-100 p-3">
                                    <span className="text-2xl">✉️</span>
                                </div>
                                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                                    Send us a{' '}
                                    <span className="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                                        Message
                                    </span>
                                </h2>
                            </div>
                            <div className="rounded-lg bg-white p-8 shadow-lg">
                                {submitStatus === 'success' && (
                                    <div className="mb-6 rounded-lg bg-green-50 p-4 border border-green-200">
                                        <p className="text-green-800 font-medium">
                                            ✅ Thank you! Your message has been sent successfully. We will get back to you soon.
                                        </p>
                                    </div>
                                )}
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div>
                                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                                Full Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-200 transition-colors"
                                                placeholder="Enter your full name"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                                Email Address *
                                            </label>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-200 transition-colors"
                                                placeholder="Enter your email address"
                                            />
                                        </div>
                                    </div>
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div>
                                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                                                Phone Number
                                            </label>
                                            <input
                                                type="tel"
                                                id="phone"
                                                name="phone"
                                                value={formData.phone}
                                                onChange={handleInputChange}
                                                className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-200 transition-colors"
                                                placeholder="Enter your phone number"
                                            />
                                        </div>
                                        <div>
                                            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                                                Subject *
                                            </label>
                                            <select
                                                id="subject"
                                                name="subject"
                                                value={formData.subject}
                                                onChange={handleInputChange}
                                                required
                                                className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-200 transition-colors"
                                            >
                                                <option value="">Select a subject</option>
                                                <option value="career">Career Opportunities</option>
                                                <option value="project">Project Inquiry</option>
                                                <option value="partnership">Partnership</option>
                                                <option value="support">Support</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                                            Message *
                                        </label>
                                        <textarea
                                            id="message"
                                            name="message"
                                            value={formData.message}
                                            onChange={handleInputChange}
                                            required
                                            rows={6}
                                            className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-200 transition-colors resize-none"
                                            placeholder="Tell us about your inquiry..."
                                        />
                                    </div>
                                    <button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="w-full rounded-lg bg-gradient-to-r from-red-600 to-pink-600 px-8 py-4 text-white font-semibold shadow-lg transition-all duration-300 hover:from-red-700 hover:to-pink-700 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {isSubmitting ? (
                                            <span className="flex items-center justify-center gap-2">
                                                <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                                </svg>
                                                Sending Message...
                                            </span>
                                        ) : (
                                            'Send Message'
                                        )}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Call to Action Section */}
            <section className="py-20 lg:py-32">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 md:px-8 lg:px-16 xl:px-40">
                    <div className="grid gap-8 md:grid-cols-2">
                        <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-100 p-8 shadow-lg">
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">
                                🚀 Ready to Start Your Journey?
                            </h3>
                            <div className="space-y-4 text-gray-700">
                                <p>
                                    Join our team of innovative professionals and be part of exciting projects that shape the future of technology.
                                </p>
                                <p>
                                    Explore our current job openings and find the perfect role that matches your skills and ambitions.
                                </p>
                            </div>
                        </div>
                        <div className="rounded-2xl bg-gradient-to-br from-green-50 to-emerald-100 p-8 shadow-lg">
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">
                                💼 Have a Project in Mind?
                            </h3>
                            <div className="space-y-4 text-gray-700">
                                <p>
                                    Let us help you bring your ideas to life with our comprehensive IT solutions and expert development team.
                                </p>
                                <p>
                                    From web applications to mobile apps, we have the expertise to deliver exceptional results.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default ContactPage;
