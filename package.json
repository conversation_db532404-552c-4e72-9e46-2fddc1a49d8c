{"name": "careers-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.3", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.3", "@rushstack/eslint-patch": "^1.11.0", "@tailwindcss/postcss": "^4.1.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.3", "typescript": "^5"}}